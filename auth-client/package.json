{"name": "auth-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.6.2", "pinia": "^2.1.7", "vue": "^3.3.8", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^20.10.0", "@vitejs/plugin-vue": "^4.5.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^5.0.0", "vue-tsc": "^1.8.22"}}