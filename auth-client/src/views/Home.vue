<template>
  <div class="max-w-4xl mx-auto">
    <div class="text-center mb-12">
      <h1 class="text-4xl font-bold text-gray-900 mb-4">Welcome to AuthenHub</h1>
      <p class="text-xl text-gray-600">Your secure authentication gateway</p>
    </div>

    <div class="bg-white shadow rounded-lg overflow-hidden">
      <div class="p-6">
        <div v-if="authStore.isAuthenticated" class="text-center">
          <h2 class="text-2xl font-semibold text-gray-800 mb-4">
            Welcome back, {{ authStore.user?.fullName }}!
          </h2>
          <p class="text-gray-600 mb-6">
            You are successfully authenticated. You can now access protected resources.
          </p>
          <router-link to="/profile" class="btn btn-primary">
            View Your Profile
          </router-link>
        </div>
        <div v-else class="text-center">
          <h2 class="text-2xl font-semibold text-gray-800 mb-4">
            Get Started
          </h2>
          <p class="text-gray-600 mb-6">
            Please login or register to access the full features of the application.
          </p>
          <div class="flex justify-center space-x-4">
            <router-link to="/login" class="btn btn-secondary">
              Login
            </router-link>
            <router-link to="/register" class="btn btn-primary">
              Register
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <div class="mt-12 grid md:grid-cols-3 gap-8">
      <div class="bg-white p-6 rounded-lg shadow">
        <div class="text-blue-500 mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
        </div>
        <h3 class="text-xl font-semibold text-gray-800 mb-2">Secure Authentication</h3>
        <p class="text-gray-600">
          Industry-standard JWT authentication with secure password hashing and token management.
        </p>
      </div>
      
      <div class="bg-white p-6 rounded-lg shadow">
        <div class="text-blue-500 mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
        </div>
        <h3 class="text-xl font-semibold text-gray-800 mb-2">Social Login</h3>
        <p class="text-gray-600">
          Seamless integration with Google and Facebook for quick and easy authentication.
        </p>
      </div>
      
      <div class="bg-white p-6 rounded-lg shadow">
        <div class="text-blue-500 mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
        </div>
        <h3 class="text-xl font-semibold text-gray-800 mb-2">Role-Based Access</h3>
        <p class="text-gray-600">
          Granular control over user permissions with role-based access control system.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()
</script>
