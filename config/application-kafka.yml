spring:
  kafka:
    # Common Kafka settings
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:29092}
    # Consumer common configuration
    consumer:
      group-id: ${KAFKA_CONSUMER_GROUP_ID:inventory-group}
      auto-offset-reset: earliest
      # Sử dụng ErrorHandlingDeserializer để bọc các deserializer khác
      key-deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
      properties:
        # Cấu hình deserializer thực tế bên trong ErrorHandlingDeserializer
        spring.deserializer.key.delegate.class: org.apache.kafka.common.serialization.StringDeserializer
        spring.deserializer.value.delegate.class: org.springframework.kafka.support.serializer.JsonDeserializer
        # Cấu hình JsonDeserializer
        spring.json.trusted.packages: com.authenhub.kafka.dto,java.util,java.lang
        spring.json.value.default.type: java.lang.Object
        # Thêm các cấu hình để xử lý type information
        spring.json.use.type.headers: false
        spring.json.type.mapping: inventoryEvent:com.authenhub.kafka.dto.InventoryEvent,salesEvent:com.authenhub.kafka.dto.SalesEvent,replenishmentEvent:com.authenhub.kafka.dto.ReplenishmentEvent
    # Producer common configuration
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      properties:
        # Cấu hình JsonSerializer để thêm type information vào header
        spring.json.add.type.headers: false
        spring.json.type.mapping: inventoryEvent:com.authenhub.kafka.dto.InventoryEvent,salesEvent:com.authenhub.kafka.dto.SalesEvent,replenishmentEvent:com.authenhub.kafka.dto.ReplenishmentEvent
    # Listener common configuration
    listener:
      ack-mode: MANUAL_IMMEDIATE
      auto-startup: false
      missing-topics-fatal: false

---
spring:
  config:
    activate:
      on-profile: dev
  kafka:
    consumer:
      properties:
        session.timeout.ms: 30000
        heartbeat.interval.ms: 10000
        max.poll.interval.ms: 300000
        max.poll.records: 100
    producer:
      properties:
        acks: all
        retries: 3
        retry.backoff.ms: 1000
        enable.idempotence: true
    listener:
      concurrency: 2
      poll-timeout: 5000
      # Chạy trên thread riêng
      type: BATCH
      idle-between-polls: 5000
      monitor-interval: 30000
      log-container-config: true

---
spring:
  config:
    activate:
      on-profile: prod
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS}
    consumer:
      properties:
        session.timeout.ms: 60000
        heartbeat.interval.ms: 20000
        max.poll.interval.ms: 600000
        max.poll.records: 500
    producer:
      properties:
        acks: all
        retries: 5
        retry.backoff.ms: 2000
        enable.idempotence: true
    listener:
      concurrency: 4
      poll-timeout: 10000
      # Chạy trên thread riêng
      type: BATCH
      idle-between-polls: 1000
      monitor-interval: 60000
      log-container-config: false
