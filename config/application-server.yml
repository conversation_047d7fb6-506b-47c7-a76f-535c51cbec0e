# ===================================================================
# SERVER CONFIGURATION
# ===================================================================

# Common server configuration
server:
  port: 8118
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain
    min-response-size: 1024

# CORS configuration
cors:
  allowed-origins: http://localhost:5173,http://localhost:5174
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: Authorization,Content-Type
  exposed-headers: "*"
  allow-credentials: true
  max-age: 3600

# JWT configuration
jwt:
  secret: 404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
  expiration: ${JWT_EXPIRATION:*********} # 1 days
  expiration-refresh-token: ${JWT_EXPIRATION:*********} # 7 days

# Application configuration
app:
  frontend-url: ${FRONTEND_URL:http://localhost:5174}

# Admin account default
admin:
  username: admin
  email: <EMAIL>
  password: Admin@123
  fullName: Administrator

# REST client configuration
rest:
  connection:
    timeout: 60000
  socket:
    timeout: 60000
pool:
  max: 10

# RSocket configuration
spring:
  rsocket:
    server:
      mapping-path: /rsocket
      transport: websocket
      port: 7000

---
spring:
  config:
    activate:
      on-profile: dev

# Server configuration for development
server:
  ssl:
    enabled: false

# SSL configuration for development
javax:
  net:
    ssl:
      trustStore: NONE
      trustStorePassword:
      trustStoreType: NONE
      verifyHostname: false

---
spring:
  config:
    activate:
      on-profile: staging

# Server configuration for staging
server:
  ssl:
    enabled: true
    key-store: classpath:keystore-staging.p12
    key-store-password: ${SSL_KEY_STORE_PASSWORD}
    key-store-type: PKCS12
    key-alias: tomcat-staging

# Application configuration for staging
app:
  frontend-url: ${FRONTEND_URL:https://staging.example.com}

# CORS configuration for staging
cors:
  allowed-origins: ${CORS_ALLOWED_ORIGINS:https://staging.example.com}

---
spring:
  config:
    activate:
      on-profile: prod

# Server configuration for production
server:
  ssl:
    enabled: true
    key-store: classpath:keystore.p12
    key-store-password: ${SSL_KEY_STORE_PASSWORD}
    key-store-type: PKCS12
    key-alias: tomcat

# Application configuration for production
app:
  frontend-url: ${FRONTEND_URL}

# CORS configuration for production
cors:
  allowed-origins: ${CORS_ALLOWED_ORIGINS}

# JWT configuration for production
jwt:
  secret: ${JWT_SECRET:404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970}
  expiration: ${JWT_EXPIRATION:*********} # 1 days
  expiration-refresh-token: ${JWT_EXPIRATION:*********} # 7 days

# Admin account for production
admin:
  username: ${ADMIN_USERNAME:admin}
  email: ${ADMIN_EMAIL:<EMAIL>}
  password: ${ADMIN_PASSWORD}
  fullName: ${ADMIN_FULL_NAME:Administrator}
