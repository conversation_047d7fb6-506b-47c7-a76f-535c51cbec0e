-- Insert sample users (assuming users table exists)
-- INSERT INTO users (id, username, email, password, full_name, role, created_at)
-- VALUES
--     ('user1', 'johndoe', '<EMAIL>', '$2a$10$hKDVYxLefVHV/vtuPhWD3OigtRyOykRLDdUAp80Z1crSoS1lFqaFS', '<PERSON>', 'USER', NOW()),
--     ('user2', 'janedoe', '<EMAIL>', '$2a$10$hKDVYxLefVHV/vtuPhWD3OigtRyOykRLDdUAp80Z1crSoS1lFqaFS', '<PERSON>', 'USER', NOW()),
--     ('admin1', 'admin', '<EMAIL>', '$2a$10$hKDVYxLefVHV/vtuPhWD3OigtRyOykRLDdUAp80Z1crSoS1lFqaFS', 'Admin User', 'ADMIN', NOW());

-- Insert sample blog crawler jobs
INSERT INTO blog_crawler_jobs (id, user_id, url, category, search_keyword, created_at, completed_at, status, total_posts, result_json)
VALUES
    (nextval('blog_crawler_job_seq'), 'user1', 'https://example.com/blog', 'Technology', 'React', NOW() - INTERVAL '2 days', NOW() - INTERVAL '2 days', 'COMPLETED', 10, '{"posts":[{"id":"1","title":"Introduction to React","url":"https://example.com/blog/post-1","date":"2023-05-15","author":"John Smith","category":"Technology","excerpt":"Learn the basics of React..."}],"totalCount":10,"sourceUrl":"https://example.com/blog","crawlDate":"2023-05-15T10:30:00"}'),
    (nextval('blog_crawler_job_seq'), 'user1', 'https://example.com/blog', 'Programming', 'Java', NOW() - INTERVAL '1 day', NOW() - INTERVAL '1 day', 'COMPLETED', 8, '{"posts":[{"id":"1","title":"Java Programming Tips","url":"https://example.com/blog/post-2","date":"2023-05-14","author":"Jane Smith","category":"Programming","excerpt":"Advanced Java programming techniques..."}],"totalCount":8,"sourceUrl":"https://example.com/blog","crawlDate":"2023-05-14T14:20:00"}'),
    (nextval('blog_crawler_job_seq'), 'user2', 'https://anotherblog.com', 'Marketing', 'SEO', NOW() - INTERVAL '3 days', NOW() - INTERVAL '3 days', 'COMPLETED', 5, '{"posts":[{"id":"1","title":"SEO Best Practices","url":"https://anotherblog.com/post-1","date":"2023-05-12","author":"Mark Johnson","category":"Marketing","excerpt":"Learn how to improve your SEO..."}],"totalCount":5,"sourceUrl":"https://anotherblog.com","crawlDate":"2023-05-12T09:15:00"}');

-- Insert sample web crawler jobs
INSERT INTO web_crawler_jobs (id, user_id, url, depth, max_pages, created_at, completed_at, status, total_pages, crawl_duration, result_json)
VALUES
    (nextval('web_crawler_job_seq'), 'user1', 'https://example.com', 2, 20, NOW() - INTERVAL '3 days', NOW() - INTERVAL '3 days', 'COMPLETED', 15, '2.5 seconds', '{"pages":[{"id":"1","url":"https://example.com/page-1","title":"Example Page 1","status":200,"contentType":"text/html","size":"10 KB"}],"totalCount":15,"baseUrl":"https://example.com","crawlDate":"2023-05-12T11:30:00","crawlDuration":"2.5 seconds","jobId":"1","status":"completed"}'),
    (nextval('web_crawler_job_seq'), 'user1', 'https://anothersite.com', 1, 10, NOW() - INTERVAL '1 day', NOW() - INTERVAL '1 day', 'COMPLETED', 8, '1.8 seconds', '{"pages":[{"id":"1","url":"https://anothersite.com/page-1","title":"Another Site Page 1","status":200,"contentType":"text/html","size":"15 KB"}],"totalCount":8,"baseUrl":"https://anothersite.com","crawlDate":"2023-05-14T16:45:00","crawlDuration":"1.8 seconds","jobId":"2","status":"completed"}'),
    (nextval('web_crawler_job_seq'), 'user2', 'https://testsite.com', 3, 30, NOW(), NULL, 'RUNNING', NULL, NULL, NULL);

-- Insert sample API tests
INSERT INTO api_tests (id, user_id, method, url, content_type, request_body, created_at, status_code, status_text, response_body, response_time, response_content_type, favorite)
VALUES
    (nextval('api_test_seq'), 'user1', 'GET', 'https://api.example.com/users', 'application/json', NULL, NOW() - INTERVAL '2 days', 200, 'OK', '{"users":[{"id":1,"name":"John Doe"},{"id":2,"name":"Jane Doe"}]}', '120 ms', 'application/json', true),
    (nextval('api_test_seq'), 'user1', 'POST', 'https://api.example.com/users', 'application/json', '{"name":"New User","email":"<EMAIL>"}', NOW() - INTERVAL '1 day', 201, 'Created', '{"id":3,"name":"New User","email":"<EMAIL>"}', '150 ms', 'application/json', false),
    (nextval('api_test_seq'), 'user2', 'GET', 'https://api.example.com/products', 'application/json', NULL, NOW() - INTERVAL '3 days', 200, 'OK', '{"products":[{"id":1,"name":"Product 1"},{"id":2,"name":"Product 2"}]}', '100 ms', 'application/json', true);

-- Insert sample data extractor jobs
INSERT INTO data_extractor_jobs (id, user_id, extraction_type, text, created_at, completed_at, status, total_items, type_counts, extraction_time, result_json)
VALUES
    (nextval('data_extractor_job_seq'), 'user1', 'text', 'Contact <NAME_EMAIL> or call ******-123-4567. John Doe is our representative.', NOW() - INTERVAL '2 days', NOW() - INTERVAL '2 days', 'COMPLETED', 3, '{"email":1,"phone":1,"name":1}', '0.5 seconds', '{"items":[{"id":"1","email":"<EMAIL>","dataType":"email","source":"text","rawText":"<EMAIL>","confidence":95},{"id":"2","phone":"******-123-4567","dataType":"phone","source":"text","rawText":"******-123-4567","confidence":90},{"id":"3","name":"John Doe","dataType":"name","source":"text","rawText":"John Doe","confidence":85}],"totalCount":3,"typeCounts":{"email":1,"phone":1,"name":1},"extractionDate":"2023-05-13T10:30:00","source":"text","extractionTime":"0.5 seconds"}'),
    (nextval('data_extractor_job_seq'), 'user2', 'web', 'https://example.com/contact', NOW() - INTERVAL '1 day', NOW() - INTERVAL '1 day', 'COMPLETED', 2, '{"email":1,"phone":1}', '0.8 seconds', '{"items":[{"id":"1","email":"<EMAIL>","dataType":"email","source":"web","rawText":"<EMAIL>","confidence":95},{"id":"2","phone":"******-987-6543","dataType":"phone","source":"web","rawText":"******-987-6543","confidence":90}],"totalCount":2,"typeCounts":{"email":1,"phone":1},"extractionDate":"2023-05-14T14:20:00","source":"web","extractionTime":"0.8 seconds"}');

-- Insert sample code generations
INSERT INTO code_generations (id, user_id, code_type, model_name, framework, language, created_at, file_name, generated_code, favorite)
VALUES
    (nextval('code_generation_seq'), 'user1', 'crud', 'User', 'express', 'javascript', NOW() - INTERVAL '2 days', 'user.js', '// Model definition\nconst User = sequelize.define(''user'', {\n  id: {\n    type: DataTypes.INTEGER,\n    primaryKey: true,\n    autoIncrement: true\n  },\n  name: {\n    type: DataTypes.STRING,\n    allowNull: false\n  },\n  email: {\n    type: DataTypes.STRING,\n    allowNull: false\n  }\n});\n\n// Controller\nclass UserController {\n  // Get all\n  async getAll(req, res) {\n    try {\n      const items = await User.findAll();\n      return res.status(200).json(items);\n    } catch (error) {\n      return res.status(500).json({ error: error.message });\n    }\n  }\n\n  // Get by ID\n  async getById(req, res) {\n    try {\n      const item = await User.findByPk(req.params.id);\n      if (!item) {\n        return res.status(404).json({ error: ''Item not found'' });\n      }\n      return res.status(200).json(item);\n    } catch (error) {\n      return res.status(500).json({ error: error.message });\n    }\n  }\n\n  // Create\n  async create(req, res) {\n    try {\n      const item = await User.create(req.body);\n      return res.status(201).json(item);\n    } catch (error) {\n      return res.status(500).json({ error: error.message });\n    }\n  }\n\n  // Update\n  async update(req, res) {\n    try {\n      const [updated] = await User.update(req.body, {\n        where: { id: req.params.id }\n      });\n      if (updated) {\n        const updatedItem = await User.findByPk(req.params.id);\n        return res.status(200).json(updatedItem);\n      }\n      return res.status(404).json({ error: ''Item not found'' });\n    } catch (error) {\n      return res.status(500).json({ error: error.message });\n    }\n  }\n\n  // Delete\n  async delete(req, res) {\n    try {\n      const deleted = await User.destroy({\n        where: { id: req.params.id }\n      });\n      if (deleted) {\n        return res.status(204).send();\n      }\n      return res.status(404).json({ error: ''Item not found'' });\n    } catch (error) {\n      return res.status(500).json({ error: error.message });\n    }\n  }\n}\n\n// Routes\nconst router = express.Router();\nconst controller = new UserController();\n\nrouter.get(''/users'', controller.getAll);\nrouter.get(''/users/:id'', controller.getById);\nrouter.post(''/users'', controller.create);\nrouter.put(''/users/:id'', controller.update);\nrouter.delete(''/users/:id'', controller.delete);\n\nmodule.exports = router;', true),
    (nextval('code_generation_seq'), 'user1', 'component', 'UserList', 'react', 'typescript', NOW() - INTERVAL '1 day', 'UserList.tsx', 'import React, { useState, useEffect } from ''react'';\nimport { Card, Button, Form, Input, Space, Table, Modal, message } from ''antd'';\n\ninterface UserListProps {\n  title?: string;\n}\n\ninterface DataItem {\n  id: string;\n  name: string;\n  email: string;\n  createdAt: string;\n}\n\nconst UserList: React.FC<UserListProps> = ({ title = ''UserList'' }) => {\n  const [data, setData] = useState<DataItem[]>([]);\n  const [loading, setLoading] = useState<boolean>(false);\n  const [modalVisible, setModalVisible] = useState<boolean>(false);\n  const [editingItem, setEditingItem] = useState<DataItem | null>(null);\n  const [form] = Form.useForm();\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const fetchData = async () => {\n    setLoading(true);\n    try {\n      // Replace with actual API call\n      const response = await fetch(''/api/users'');\n      const result = await response.json();\n      setData(result);\n    } catch (error) {\n      message.error(''Failed to fetch data'');\n      console.error(error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAdd = () => {\n    setEditingItem(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (record: DataItem) => {\n    setEditingItem(record);\n    form.setFieldsValue(record);\n    setModalVisible(true);\n  };\n\n  const handleDelete = async (id: string) => {\n    try {\n      // Replace with actual API call\n      await fetch(`/api/users/${id}`, { method: ''DELETE'' });\n      message.success(''User deleted successfully'');\n      fetchData();\n    } catch (error) {\n      message.error(''Failed to delete user'');\n      console.error(error);\n    }\n  };\n\n  const handleSubmit = async (values: any) => {\n    try {\n      if (editingItem) {\n        // Update existing item\n        await fetch(`/api/users/${editingItem.id}`, {\n          method: ''PUT'',\n          headers: { ''Content-Type'': ''application/json'' },\n          body: JSON.stringify(values)\n        });\n        message.success(''User updated successfully'');\n      } else {\n        // Create new item\n        await fetch(''/api/users'', {\n          method: ''POST'',\n          headers: { ''Content-Type'': ''application/json'' },\n          body: JSON.stringify(values)\n        });\n        message.success(''User created successfully'');\n      }\n      setModalVisible(false);\n      fetchData();\n    } catch (error) {\n      message.error(''Failed to save user'');\n      console.error(error);\n    }\n  };\n\n  const columns = [\n    {\n      title: ''Name'',\n      dataIndex: ''name'',\n      key: ''name'',\n    },\n    {\n      title: ''Email'',\n      dataIndex: ''email'',\n      key: ''email'',\n    },\n    {\n      title: ''Created At'',\n      dataIndex: ''createdAt'',\n      key: ''createdAt'',\n    },\n    {\n      title: ''Actions'',\n      key: ''actions'',\n      render: (text: string, record: DataItem) => (\n        <Space>\n          <Button type="link" onClick={() => handleEdit(record)}>Edit</Button>\n          <Button type="link" danger onClick={() => handleDelete(record.id)}>Delete</Button>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <Card title={title}>\n      <Button type="primary" onClick={handleAdd} style={{ marginBottom: 16 }}>\n        Add User\n      </Button>\n      <Table\n        columns={columns}\n        dataSource={data}\n        rowKey="id"\n        loading={loading}\n      />\n      <Modal\n        title={editingItem ? ''Edit User'' : ''Add User''}\n        visible={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        footer={null}\n      >\n        <Form form={form} onFinish={handleSubmit} layout="vertical">\n          <Form.Item\n            name="name"\n            label="Name"\n            rules={[{ required: true, message: ''Please enter a name'' }]}\n          >\n            <Input />\n          </Form.Item>\n          <Form.Item\n            name="email"\n            label="Email"\n            rules={[{ required: true, message: ''Please enter an email'' }]}\n          >\n            <Input />\n          </Form.Item>\n          <Form.Item>\n            <Button type="primary" htmlType="submit">\n              {editingItem ? ''Update'' : ''Create''}\n            </Button>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </Card>\n  );\n};\n\nexport default UserList;', false);

-- Insert sample chat sessions
INSERT INTO chat_sessions (id, session_id, user_id, created_at, last_message_at, title, message_count, token_count, model)
VALUES
    (nextval('chat_session_seq'), 'session1', 'user1', NOW() - INTERVAL '2 days', NOW() - INTERVAL '1 day', 'General Questions', 5, 250, 'gpt-3.5'),
    (nextval('chat_session_seq'), 'session2', 'user1', NOW() - INTERVAL '1 day', NOW(), 'Programming Help', 3, 180, 'gpt-3.5'),
    (nextval('chat_session_seq'), 'session3', 'user2', NOW() - INTERVAL '3 days', NOW() - INTERVAL '3 days', 'Marketing Advice', 2, 120, 'gpt-3.5');

-- Insert sample chat messages
INSERT INTO chat_messages (id, message_id, session_id, content, sender, timestamp, token_count, model)
VALUES
    (nextval('chat_message_seq'), 'msg1', 'session1', 'Hello, how can I help you today?', 'bot', NOW() - INTERVAL '2 days', 10, 'gpt-3.5'),
    (nextval('chat_message_seq'), 'msg2', 'session1', 'I need help with a programming problem.', 'user', NOW() - INTERVAL '2 days' + INTERVAL '1 minute', 12, NULL),
    (nextval('chat_message_seq'), 'msg3', 'session1', 'Sure, I''d be happy to help. What programming language are you working with?', 'bot', NOW() - INTERVAL '2 days' + INTERVAL '2 minutes', 20, 'gpt-3.5'),
    (nextval('chat_message_seq'), 'msg4', 'session1', 'I''m working with Java.', 'user', NOW() - INTERVAL '2 days' + INTERVAL '3 minutes', 8, NULL),
    (nextval('chat_message_seq'), 'msg5', 'session1', 'Great! Java is a versatile language. Please describe the problem you''re facing.', 'bot', NOW() - INTERVAL '2 days' + INTERVAL '4 minutes', 18, 'gpt-3.5'),
    
    (nextval('chat_message_seq'), 'msg6', 'session2', 'I need help with React hooks.', 'user', NOW() - INTERVAL '1 day', 10, NULL),
    (nextval('chat_message_seq'), 'msg7', 'session2', 'React hooks are functions that let you "hook into" React state and lifecycle features from function components. What specific hook are you having trouble with?', 'bot', NOW() - INTERVAL '1 day' + INTERVAL '1 minute', 30, 'gpt-3.5'),
    (nextval('chat_message_seq'), 'msg8', 'session2', 'I''m having issues with useEffect and cleanup.', 'user', NOW() - INTERVAL '1 day' + INTERVAL '2 minutes', 12, NULL),
    
    (nextval('chat_message_seq'), 'msg9', 'session3', 'Can you help me with SEO strategies?', 'user', NOW() - INTERVAL '3 days', 10, NULL),
    (nextval('chat_message_seq'), 'msg10', 'session3', 'Absolutely! SEO (Search Engine Optimization) is crucial for online visibility. Here are some key strategies: 1) Keyword research, 2) Quality content creation, 3) On-page optimization, 4) Technical SEO, 5) Link building. Which area would you like to focus on?', 'bot', NOW() - INTERVAL '3 days' + INTERVAL '1 minute', 45, 'gpt-3.5');

-- Insert sample affiliate scraper jobs
INSERT INTO affiliate_scraper_jobs (id, user_id, platform, keyword, category, min_price, max_price, created_at, completed_at, status, total_products, total_commission, result_json)
VALUES
    (nextval('affiliate_scraper_job_seq'), 'user1', 'shopee', 'smartphone', 'Electronics', 200.0, 1000.0, NOW() - INTERVAL '2 days', NOW() - INTERVAL '2 days', 'COMPLETED', 10, 250.0, '{"products":[{"id":"1","name":"Smartphone X","url":"https://shopee.vn/product-1","originalPrice":500.0,"salePrice":450.0,"discountPercent":10.0,"platform":"shopee","commission":22.5}],"totalCount":10,"platform":"shopee","keyword":"smartphone","scrapeDate":"2023-05-13T10:30:00","totalCommission":250.0}'),
    (nextval('affiliate_scraper_job_seq'), 'user1', 'lazada', 'laptop', 'Electronics', 500.0, 2000.0, NOW() - INTERVAL '1 day', NOW() - INTERVAL '1 day', 'COMPLETED', 8, 320.0, '{"products":[{"id":"1","name":"Laptop Pro","url":"https://lazada.vn/product-1","originalPrice":1500.0,"salePrice":1350.0,"discountPercent":10.0,"platform":"lazada","commission":67.5}],"totalCount":8,"platform":"lazada","keyword":"laptop","scrapeDate":"2023-05-14T14:20:00","totalCommission":320.0}'),
    (nextval('affiliate_scraper_job_seq'), 'user2', 'tiki', 'headphones', 'Electronics', 50.0, 200.0, NOW(), NULL, 'RUNNING', NULL, NULL, NULL);

-- Insert sample tracked products
INSERT INTO tracked_products (id, user_id, product_id, platform, name, url, image_url, original_price, current_price, lowest_price, highest_price, created_at, last_checked_at, notify_on_price_change, target_price, notify_on_target_price)
VALUES
    (nextval('tracked_product_seq'), 'user1', 'prod1', 'shopee', 'Smartphone X', 'https://shopee.vn/product-1', 'https://shopee.vn/images/product-1.jpg', 500.0, 450.0, 450.0, 500.0, NOW() - INTERVAL '2 days', NOW(), true, 400.0, true),
    (nextval('tracked_product_seq'), 'user1', 'prod2', 'lazada', 'Laptop Pro', 'https://lazada.vn/product-1', 'https://lazada.vn/images/product-1.jpg', 1500.0, 1350.0, 1350.0, 1500.0, NOW() - INTERVAL '1 day', NOW(), true, 1200.0, true),
    (nextval('tracked_product_seq'), 'user2', 'prod3', 'tiki', 'Wireless Headphones', 'https://tiki.vn/product-1', 'https://tiki.vn/images/product-1.jpg', 150.0, 120.0, 120.0, 150.0, NOW() - INTERVAL '3 days', NOW(), true, 100.0, true);

-- Insert sample price history
INSERT INTO price_history (id, tracked_product_id, price, timestamp, discount_percent, is_lowest_price, is_highest_price)
VALUES
    (nextval('price_history_seq'), 1, 500.0, NOW() - INTERVAL '2 days', 0.0, false, true),
    (nextval('price_history_seq'), 1, 480.0, NOW() - INTERVAL '1 day', 4.0, false, false),
    (nextval('price_history_seq'), 1, 450.0, NOW(), 10.0, true, false),
    
    (nextval('price_history_seq'), 2, 1500.0, NOW() - INTERVAL '1 day', 0.0, false, true),
    (nextval('price_history_seq'), 2, 1350.0, NOW(), 10.0, true, false),
    
    (nextval('price_history_seq'), 3, 150.0, NOW() - INTERVAL '3 days', 0.0, false, true),
    (nextval('price_history_seq'), 3, 135.0, NOW() - INTERVAL '2 days', 10.0, false, false),
    (nextval('price_history_seq'), 3, 120.0, NOW() - INTERVAL '1 day', 20.0, true, false);
