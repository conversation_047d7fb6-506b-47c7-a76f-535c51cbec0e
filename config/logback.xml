<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true" scan="true" scanPeriod="10 seconds">

    <property name="log-path" value="/var/logs/admin-hub"/>
    <property name="name-file" value="admin"/>
    <property name="time-file" value="20"/>
    <property name="size-file" value="20 MB"/>
    <property name="total-size-file" value="200 GB"/>
    <property name="default-max-history" value="10000"/>
    <property name="default-pattern-layout" value="%d %25.25M:%-4.4L - %-5level - [%X{token}] - %m %n "/>
    <property name="debug-pattern-layout" value="%d %25.25M:%-4.4L - %-5level - [%X{token}] - %m %n"/>

    <appender name="info-log" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log-path}/${name-file}.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log-path}/%d{yyyy-MM}/${name-file}-%d{yyyyMMdd}-%i.log.gz</fileNamePattern>
            <maxFileSize>${size-file}</maxFileSize>
            <totalSizeCap>${total-size-file}</totalSizeCap>
            <maxHistory>${default-max-history}</maxHistory>
        </rollingPolicy>

        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>

        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.authenhub.config.logback.MaskingPatternLayout">
                <maskPattern>(\n?"(?i)password"\s?:\s?"(.*?)")</maskPattern>
                <maskPattern>((?i)password\s?[=\-:]\s?[\[\']?(.*?)[\]\'.\s])</maskPattern>
                <pattern>${default-pattern-layout}</pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="warn-log" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log-path}/${name-file}-warn.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log-path}/%d{yyyy-MM}/${name-file}-warn-%d{yyyyMMdd}-%i.log.gz</fileNamePattern>
            <maxFileSize>${size-file}</maxFileSize>
            <totalSizeCap>${total-size-file}</totalSizeCap>
            <maxHistory>${default-max-history}</maxHistory>
        </rollingPolicy>

        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>

        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.authenhub.config.logback.MaskingPatternLayout">
                <maskPattern>(\n?"(?i)password"\s?:\s?"(.*?)")</maskPattern>
                <maskPattern>((?i)password\s?[=\-:]\s?[\[\']?(.*?)[\]\'.\s])</maskPattern>
                <pattern>${default-pattern-layout}</pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="error-log" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log-path}/${name-file}-error.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log-path}/%d{yyyy-MM}/${name-file}-error-%d{yyyyMMdd}-%i.log.gz</fileNamePattern>
            <maxFileSize>${size-file}</maxFileSize>
            <totalSizeCap>${total-size-file}</totalSizeCap>
            <maxHistory>${default-max-history}</maxHistory>
        </rollingPolicy>

        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>

        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.authenhub.config.logback.MaskingPatternLayout">
                <maskPattern>(\n?"(?i)password"\s?:\s?"(.*?)")</maskPattern>
                <maskPattern>((?i)password\s?[=\-:]\s?[\[\']?(.*?)[\]\'.\s])</maskPattern>
                <pattern>${default-pattern-layout}</pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="debug-log" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log-path}/${name-file}-debug.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log-path}/%d{yyyy-MM}/${name-file}-debug-%d{yyyyMMdd}-%i.log.gz</fileNamePattern>
            <maxFileSize>${size-file}</maxFileSize>
            <totalSizeCap>${total-size-file}</totalSizeCap>
            <maxHistory>${default-max-history}</maxHistory>
        </rollingPolicy>

        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>

        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.authenhub.config.logback.MaskingPatternLayout">
                <maskPattern>(\n?"(?i)password"\s?:\s?"(.*?)")</maskPattern>
                <maskPattern>((?i)password\s?[=\-:]\s?[\[\']?(.*?)[\]\'.\s])</maskPattern>
                <pattern>${debug-pattern-layout}</pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>
                ${default-pattern-layout}
            </pattern>
        </encoder>
    </appender>

    <logger name="java.sql.PreparedStatement" value="DEBUG"/>
    <logger name="java.sql.Connection" value="DEBUG"/>
    <logger name="java.sql.Statement" value="DEBUG"/>

    <Logger name="com.authenhub" additivity="false" level="debug">
        <appender-ref ref="warn-log" level="warn"/>
        <appender-ref ref="error-log" level="error"/>
        <appender-ref ref="debug-log" level="debug"/>
        <appender-ref ref="info-log" level="info"/>
        <!-- Console output -->
        <appender-ref ref="STDOUT" level="debug"/>
    </Logger>

    <Logger name="org.springframework" additivity="false" level="debug">
        <appender-ref ref="warn-log" level="warn"/>
        <appender-ref ref="error-log" level="error"/>
        <appender-ref ref="debug-log" level="debug"/>
        <appender-ref ref="info-log" level="info"/>
        <!-- Console output -->
        <appender-ref ref="STDOUT" level="debug"/>
    </Logger>

    <!-- Root logger - catch all other packages -->
    <root level="info">
        <appender-ref ref="STDOUT" />
        <appender-ref ref="info-log" />
        <appender-ref ref="warn-log" />
        <appender-ref ref="error-log" />
        <appender-ref ref="debug-log" />
    </root>
</configuration>
