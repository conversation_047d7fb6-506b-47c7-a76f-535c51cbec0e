package com.authenhub.config;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.info.Contact;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.info.License;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.servers.Server;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.servers.ServerVariable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.List;

/**
 * Cấu hình OpenAPI (Swagger) cho ứng dụng
 */
@Configuration
@OpenAPIDefinition(
        info = @Info(
                title = "AuthenHub API",
                version = "1.0.0",
                description = "API documentation for AuthenHub - Authentication and Password Management System",
                contact = @Contact(
                        name = "AuthenHub Support",
                        email = "<EMAIL>",
                        url = "https://authenhub.com"
                ),
                license = @License(
                        name = "Apache 2.0",
                        url = "https://www.apache.org/licenses/LICENSE-2.0"
                )
        ),
        servers = {
                @Server(
                        url = "http://localhost:{port}",
                        description = "Local Development Server",
                        variables = {
                                @io.swagger.v3.oas.annotations.servers.ServerVariable(
                                        name = "port",
                                        defaultValue = "8118",
                                        description = "Server port"
                                )
                        }
                ),
                @Server(
                        url = "https://api.authenhub.com",
                        description = "Production Server"
                )
        },
        security = {
                @SecurityRequirement(name = "bearerAuth")
        }
)
@SecurityScheme(
        name = "bearerAuth",
        description = "JWT authentication with bearer token",
        scheme = "bearer",
        type = SecuritySchemeType.HTTP,
        bearerFormat = "JWT",
        in = SecuritySchemeIn.HEADER
)
public class OpenApiConfig {

    @Value("${server.port:8118}")
    private String serverPort;

    /**
     * Cấu hình bổ sung cho OpenAPI
     *
     * @return OpenAPI object
     */
    @Bean
    public OpenAPI customOpenAPI() {
        Schema<?> passwordSchema = new Schema<>()
                .description("Mật khẩu đã được mã hóa")
                .example("********");

        // Create a server with the dynamic port
        io.swagger.v3.oas.models.servers.Server localServer = new io.swagger.v3.oas.models.servers.Server()
                .url("http://localhost:" + serverPort)
                .description("Local Development Server");

        // Create a production server
        io.swagger.v3.oas.models.servers.Server prodServer = new io.swagger.v3.oas.models.servers.Server()
                .url("https://api.authenhub.com")
                .description("Production Server");

        return new OpenAPI()
                .servers(List.of(localServer, prodServer))
                .components(new Components()
                        .addSchemas("Password", passwordSchema)
                        .addSecuritySchemes("bearer-key",
                                new io.swagger.v3.oas.models.security.SecurityScheme()
                                        .type(io.swagger.v3.oas.models.security.SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT"))
                );
    }
}
