@echo off
echo Testing Kafka by sending a test message...

echo Creating a test message...
echo {"eventId":"test-123","eventType":"TEST","sku":"TEST-001","productName":"Test Product","newQuantity":100,"userId":"system","timestamp":"2023-05-09T12:00:00"} > test-message.json

echo Sending message to inventory-changes topic...
docker exec -i kafka kafka-console-producer --bootstrap-server kafka:9092 --topic inventory-changes < test-message.json

echo Message sent! Now consuming the message...
docker exec kafka kafka-console-consumer --bootstrap-server kafka:9092 --topic inventory-changes --from-beginning --max-messages 1

echo Test completed.
echo Press any key to exit...
pause > nul
